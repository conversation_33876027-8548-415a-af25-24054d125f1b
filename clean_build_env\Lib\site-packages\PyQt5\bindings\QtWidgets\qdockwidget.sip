// qdockwidget.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDockWidget : QWidget
{
%TypeHeaderCode
#include <qdockwidget.h>
%End

public:
    QDockWidget(const QString &title, QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    QDockWidget(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QDockWidget();
    QWidget *widget() const;
    void setWidget(QWidget *widget /Transfer/);

    enum DockWidgetFeature
    {
        DockWidgetClosable,
        DockWidgetMovable,
        DockWidgetFloatable,
        DockWidgetVerticalTitleBar,
        AllDockWidgetFeatures,
        NoDockWidgetFeatures,
    };

    typedef QFlags<QDockWidget::DockWidgetFeature> DockWidgetFeatures;
    void setFeatures(QDockWidget::DockWidgetFeatures features);
    QDockWidget::DockWidgetFeatures features() const;
    void setFloating(bool floating);
    bool isFloating() const;
    void setAllowedAreas(Qt::DockWidgetAreas areas);
    Qt::DockWidgetAreas allowedAreas() const;
    bool isAreaAllowed(Qt::DockWidgetArea area) const;
    QAction *toggleViewAction() const /Transfer/;
    void setTitleBarWidget(QWidget *widget /Transfer/);
    QWidget *titleBarWidget() const;

signals:
    void featuresChanged(QDockWidget::DockWidgetFeatures features);
    void topLevelChanged(bool topLevel);
    void allowedAreasChanged(Qt::DockWidgetAreas allowedAreas);
    void dockLocationChanged(Qt::DockWidgetArea area);
    void visibilityChanged(bool visible);

protected:
    void initStyleOption(QStyleOptionDockWidget *option) const;
    virtual void changeEvent(QEvent *event);
    virtual void closeEvent(QCloseEvent *event);
    virtual void paintEvent(QPaintEvent *event);
    virtual bool event(QEvent *event);
};

QFlags<QDockWidget::DockWidgetFeature> operator|(QDockWidget::DockWidgetFeature f1, QFlags<QDockWidget::DockWidgetFeature> f2);
