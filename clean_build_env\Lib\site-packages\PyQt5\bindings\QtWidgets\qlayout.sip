// qlayout.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QLayout : QObject, QLayoutItem
{
%TypeHeaderCode
#include <qlayout.h>
%End

public:
    enum SizeConstraint
    {
        SetDefaultConstraint,
        SetNoConstraint,
        SetMinimumSize,
        SetFixedSize,
        SetMaximumSize,
        SetMinAndMaxSize,
    };

    QLayout(QWidget *parent /TransferThis/);
    QLayout();
    virtual ~QLayout();
    int spacing() const;
    void setSpacing(int);
    bool setAlignment(QWidget *w, Qt::Alignment alignment);
    bool setAlignment(QLayout *l, Qt::Alignment alignment);
    void setAlignment(Qt::Alignment alignment);
    void setSizeConstraint(QLayout::SizeConstraint);
    QLayout::SizeConstraint sizeConstraint() const;
    void setMenuBar(QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->setMenuBar(a0);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (a0 && parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows setMenuBar(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    QWidget *menuBar() const;
    QWidget *parentWidget() const;
    virtual void invalidate();
    virtual QRect geometry() const;
    bool activate();
    void update();
    void addWidget(QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        sipCpp->addWidget(a0);
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows addWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    virtual void addItem(QLayoutItem * /Transfer/) = 0;
    void removeWidget(QWidget *w /TransferBack/);
    void removeItem(QLayoutItem * /TransferBack/);
    virtual Qt::Orientations expandingDirections() const;
    virtual QSize minimumSize() const;
    virtual QSize maximumSize() const;
    virtual void setGeometry(const QRect &);
    virtual QLayoutItem *itemAt(int index) const = 0;
    virtual QLayoutItem *takeAt(int index) = 0 /TransferBack/;
    virtual int indexOf(QWidget *) const;
%If (Qt_5_12_0 -)
    int indexOf(QLayoutItem *) const;
%End
    virtual int count() const = 0 /__len__/;
    virtual bool isEmpty() const;
    int totalHeightForWidth(int w) const;
    QSize totalMinimumSize() const;
    QSize totalMaximumSize() const;
    QSize totalSizeHint() const;
    virtual QLayout *layout();
    void setEnabled(bool);
    bool isEnabled() const;
    static QSize closestAcceptableSize(const QWidget *w, const QSize &s);

protected:
    void widgetEvent(QEvent *);
    virtual void childEvent(QChildEvent *e);
    void addChildLayout(QLayout *l /Transfer/);
    void addChildWidget(QWidget *w /GetWrapper/);
%MethodCode
        Py_BEGIN_ALLOW_THREADS
        #if defined(SIP_PROTECTED_IS_PUBLIC)
        sipCpp->addChildWidget(a0);
        #else
        sipCpp->sipProtect_addChildWidget(a0);
        #endif
        Py_END_ALLOW_THREADS
        
        // The layout's parent widget (if there is one) will now have ownership.
        QWidget *parent = sipCpp->parentWidget();
        
        if (parent)
        {
            PyObject *py_parent = sipGetPyObject(parent, sipType_QWidget);
        
            if (py_parent)
                sipTransferTo(a0Wrapper, py_parent);
        }
        else
        {
            // For now give the Python ownership to the layout.  This maintains
            // compatibility with previous versions and allows
            // addChildWidget(QWidget()).
            sipTransferTo(a0Wrapper, sipSelf);
        }
%End

    QRect alignmentRect(const QRect &) const;

public:
    void setContentsMargins(int left, int top, int right, int bottom);
    void getContentsMargins(int *left, int *top, int *right, int *bottom) const;
    QRect contentsRect() const;
    void setContentsMargins(const QMargins &margins);
    QMargins contentsMargins() const;
    virtual QSizePolicy::ControlTypes controlTypes() const;
%If (Qt_5_2_0 -)
    QLayoutItem *replaceWidget(QWidget *from, QWidget *to /Transfer/, Qt::FindChildOptions options = Qt::FindChildrenRecursively) /TransferBack/;
%End
};
