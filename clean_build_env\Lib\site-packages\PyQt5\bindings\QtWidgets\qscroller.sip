// qscroller.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScroller : QObject
{
%TypeHeaderCode
#include <qscroller.h>
%End

public:
    enum State
    {
        Inactive,
        Pressed,
        Dragging,
        Scrolling,
    };

    enum ScrollerGestureType
    {
        TouchGesture,
        LeftMouseButtonGesture,
        RightMouseButtonGesture,
        MiddleMouseButtonGesture,
    };

    enum Input
    {
        InputPress,
        InputMove,
        InputRelease,
    };

    static bool hasScroller(QObject *target);
    static QScroller *scroller(QObject *target);
    static Qt::GestureType grabGesture(QObject *target, QScroller::ScrollerGestureType scrollGestureType = QScroller::TouchGesture);
    static Qt::GestureType grabbedGesture(QObject *target);
    static void ungrabGesture(QObject *target);
    static QList<QScroller *> activeScrollers();
    QObject *target() const;
    QScroller::State state() const;
    bool handleInput(QScroller::Input input, const QPointF &position, qint64 timestamp = 0);
    void stop();
    QPointF velocity() const;
    QPointF finalPosition() const;
    QPointF pixelPerMeter() const;
    QScrollerProperties scrollerProperties() const;
    void setSnapPositionsX(const QList<qreal> &positions);
    void setSnapPositionsX(qreal first, qreal interval);
    void setSnapPositionsY(const QList<qreal> &positions);
    void setSnapPositionsY(qreal first, qreal interval);

public slots:
    void setScrollerProperties(const QScrollerProperties &prop);
    void scrollTo(const QPointF &pos);
    void scrollTo(const QPointF &pos, int scrollTime);
    void ensureVisible(const QRectF &rect, qreal xmargin, qreal ymargin);
    void ensureVisible(const QRectF &rect, qreal xmargin, qreal ymargin, int scrollTime);
    void resendPrepareEvent();

signals:
    void stateChanged(QScroller::State newstate);
    void scrollerPropertiesChanged(const QScrollerProperties &);

private:
    QScroller(QObject *target);
    virtual ~QScroller();
    QScroller(const QScroller &);
};
