# -*- coding: utf-8 -*-
"""
主窗口模块
"""

import sys
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTabWidget, QLabel, QPushButton, QTextEdit, 
                             QLineEdit, QCheckBox, QSpinBox, QGroupBox,
                             QMessageBox, QApplication)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QIcon, QFont


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    service_start_requested = pyqtSignal()
    service_stop_requested = pyqtSignal()
    config_changed = pyqtSignal(str, object)
    
    def __init__(self, config_manager=None, message_handler=None):
        super().__init__()
        self.config_manager = config_manager
        self.message_handler = message_handler
        self.is_service_running = False
        
        self.init_ui()
        self.setup_connections()
        self.load_config()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("企业微信自动回复系统")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡控件
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个选项卡
        self.create_config_tab()
        self.create_status_tab()
        self.create_log_tab()
        
        # 创建底部控制栏
        self.create_control_bar(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #0078d4;
            }
            QPushButton {
                background-color: #0078d4;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #106ebe;
            }
            QPushButton:pressed {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
    
    def create_config_tab(self):
        """创建配置选项卡"""
        config_widget = QWidget()
        layout = QVBoxLayout(config_widget)
        
        # Mock配置组
        mock_group = QGroupBox("Mock模式配置")
        mock_layout = QVBoxLayout(mock_group)
        
        # Mock启用复选框
        self.mock_enabled_cb = QCheckBox("启用Mock模式")
        mock_layout.addWidget(self.mock_enabled_cb)
        
        # 自动回复文本
        mock_layout.addWidget(QLabel("自动回复文本:"))
        self.reply_text_edit = QTextEdit()
        self.reply_text_edit.setMaximumHeight(100)
        mock_layout.addWidget(self.reply_text_edit)
        
        # 回复延迟
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("回复延迟(秒):"))
        self.reply_delay_spin = QSpinBox()
        self.reply_delay_spin.setRange(1, 60)
        self.reply_delay_spin.setValue(2)
        delay_layout.addWidget(self.reply_delay_spin)
        delay_layout.addStretch()
        mock_layout.addLayout(delay_layout)
        
        # 消息间隔
        interval_layout = QHBoxLayout()
        interval_layout.addWidget(QLabel("消息间隔(秒):"))
        self.message_interval_spin = QSpinBox()
        self.message_interval_spin.setRange(5, 300)
        self.message_interval_spin.setValue(10)
        interval_layout.addWidget(self.message_interval_spin)
        interval_layout.addStretch()
        mock_layout.addLayout(interval_layout)
        
        layout.addWidget(mock_group)
        
        # 窗口配置组
        window_group = QGroupBox("窗口配置")
        window_layout = QVBoxLayout(window_group)
        
        self.minimize_to_tray_cb = QCheckBox("最小化到系统托盘")
        window_layout.addWidget(self.minimize_to_tray_cb)
        
        self.start_minimized_cb = QCheckBox("启动时最小化")
        window_layout.addWidget(self.start_minimized_cb)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
        
        # 保存按钮
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config)
        layout.addWidget(save_btn)
        
        self.tab_widget.addTab(config_widget, "配置")
    
    def create_status_tab(self):
        """创建状态选项卡"""
        status_widget = QWidget()
        layout = QVBoxLayout(status_widget)
        
        # 服务状态组
        service_group = QGroupBox("服务状态")
        service_layout = QVBoxLayout(service_group)
        
        self.service_status_label = QLabel("状态: 已停止")
        self.service_status_label.setFont(QFont("Arial", 12, QFont.Bold))
        service_layout.addWidget(self.service_status_label)
        
        self.uptime_label = QLabel("运行时间: --")
        service_layout.addWidget(self.uptime_label)
        
        layout.addWidget(service_group)
        
        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QVBoxLayout(stats_group)
        
        self.message_count_label = QLabel("接收消息数: 0")
        stats_layout.addWidget(self.message_count_label)
        
        self.reply_count_label = QLabel("发送回复数: 0")
        stats_layout.addWidget(self.reply_count_label)
        
        self.last_activity_label = QLabel("最后活动: --")
        stats_layout.addWidget(self.last_activity_label)
        
        layout.addWidget(stats_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(status_widget, "状态")
    
    def create_log_tab(self):
        """创建日志选项卡"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        layout.addWidget(QLabel("系统日志:"))
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_btn_layout = QHBoxLayout()
        
        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_btn_layout.addWidget(clear_log_btn)
        
        log_btn_layout.addStretch()
        layout.addLayout(log_btn_layout)
        
        self.tab_widget.addTab(log_widget, "日志")
    
    def create_control_bar(self, main_layout):
        """创建底部控制栏"""
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("启动服务")
        self.start_btn.clicked.connect(self.start_service)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("停止服务")
        self.stop_btn.clicked.connect(self.stop_service)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        control_layout.addStretch()
        
        exit_btn = QPushButton("退出")
        exit_btn.clicked.connect(self.close)
        control_layout.addWidget(exit_btn)
        
        main_layout.addLayout(control_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        # 配置变更信号
        self.mock_enabled_cb.toggled.connect(
            lambda checked: self.config_changed.emit("mock_enabled", checked)
        )
        self.reply_text_edit.textChanged.connect(
            lambda: self.config_changed.emit("mock_reply_text", self.reply_text_edit.toPlainText())
        )
        self.reply_delay_spin.valueChanged.connect(
            lambda value: self.config_changed.emit("mock_reply_delay", value)
        )
        self.message_interval_spin.valueChanged.connect(
            lambda value: self.config_changed.emit("mock_message_interval", value)
        )
    
    def load_config(self):
        """加载配置到界面"""
        if not self.config_manager:
            return
        
        config = self.config_manager.get_all_config()
        
        self.mock_enabled_cb.setChecked(config.get("mock_enabled", True))
        self.reply_text_edit.setPlainText(config.get("mock_reply_text", ""))
        self.reply_delay_spin.setValue(config.get("mock_reply_delay", 2))
        self.message_interval_spin.setValue(config.get("mock_message_interval", 10))
        self.minimize_to_tray_cb.setChecked(config.get("minimize_to_tray", True))
        self.start_minimized_cb.setChecked(config.get("start_minimized", False))
    
    def save_config(self):
        """保存配置"""
        if not self.config_manager:
            return
        
        config_updates = {
            "mock_enabled": self.mock_enabled_cb.isChecked(),
            "mock_reply_text": self.reply_text_edit.toPlainText(),
            "mock_reply_delay": self.reply_delay_spin.value(),
            "mock_message_interval": self.message_interval_spin.value(),
            "minimize_to_tray": self.minimize_to_tray_cb.isChecked(),
            "start_minimized": self.start_minimized_cb.isChecked(),
        }
        
        if self.config_manager.update_config(config_updates):
            if self.config_manager.save_config():
                QMessageBox.information(self, "成功", "配置保存成功！")
            else:
                QMessageBox.warning(self, "错误", "配置保存失败！")
        else:
            QMessageBox.warning(self, "错误", "配置更新失败！")
    
    def start_service(self):
        """启动服务"""
        if self.message_handler:
            if self.message_handler.start_service():
                self.is_service_running = True
                self.start_btn.setEnabled(False)
                self.stop_btn.setEnabled(True)
                self.add_log("✅ 服务启动成功")
            else:
                self.add_log("❌ 服务启动失败")
    
    def stop_service(self):
        """停止服务"""
        if self.message_handler:
            if self.message_handler.stop_service():
                self.is_service_running = False
                self.start_btn.setEnabled(True)
                self.stop_btn.setEnabled(False)
                self.add_log("✅ 服务已停止")
            else:
                self.add_log("❌ 服务停止失败")
    
    def update_status(self):
        """更新状态显示"""
        if self.message_handler:
            status = self.message_handler.get_status()
            
            # 更新服务状态
            if status["service_running"]:
                self.service_status_label.setText("状态: 运行中")
                self.service_status_label.setStyleSheet("color: green;")
            else:
                self.service_status_label.setText("状态: 已停止")
                self.service_status_label.setStyleSheet("color: red;")
            
            # 更新统计信息
            mock_status = status.get("mock_engine_status", {})
            self.message_count_label.setText(f"接收消息数: {mock_status.get('message_count', 0)}")
            self.reply_count_label.setText(f"发送回复数: {mock_status.get('reply_count', 0)}")
            self.last_activity_label.setText(f"最后活动: {mock_status.get('last_activity', '--')}")
            self.uptime_label.setText(f"运行时间: {mock_status.get('uptime', '--')}")
    
    def add_log(self, message: str):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.config_manager and self.config_manager.get_value("minimize_to_tray", True):
            event.ignore()
            self.hide()
            # 这里应该显示托盘通知，但简化版暂时省略
        else:
            if self.is_service_running:
                self.stop_service()
            event.accept()
