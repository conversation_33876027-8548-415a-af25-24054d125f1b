// qdial.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QDial : QAbstractSlider
{
%TypeHeaderCode
#include <qdial.h>
%End

public:
    explicit QDial(QWidget *parent /TransferThis/ = 0);
    virtual ~QDial();
    bool wrapping() const;
    int notchSize() const;
    void setNotchTarget(double target);
    qreal notchTarget() const;
    bool notchesVisible() const;
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;

public slots:
    void setNotchesVisible(bool visible);
    void setWrapping(bool on);

protected:
    void initStyleOption(QStyleOptionSlider *option) const;
    virtual bool event(QEvent *e);
    virtual void resizeEvent(QResizeEvent *re);
    virtual void paintEvent(QPaintEvent *pe);
    virtual void mousePressEvent(QMouseEvent *me);
    virtual void mouseReleaseEvent(QMouseEvent *me);
    virtual void mouseMoveEvent(QMouseEvent *me);
    virtual void sliderChange(QAbstractSlider::SliderChange change);
};
