# -*- coding: utf-8 -*-
"""
默认配置定义
"""

DEFAULT_CONFIG = {
    # 基础应用配置
    "app_name": "企业微信自动回复系统",
    "app_version": "1.0.0",
    "debug": True,
    
    # GUI配置
    "window_width": 800,
    "window_height": 600,
    "window_title": "企业微信自动回复系统",
    "minimize_to_tray": True,
    "start_minimized": False,
    
    # Mock模式配置
    "mock_enabled": True,
    "mock_auto_reply": True,
    "mock_reply_text": "这是一个自动回复消息，当前为Mock模式。",
    "mock_reply_delay": 2,  # 秒
    "mock_message_interval": 10,  # 秒
    
    # 日志配置
    "log_level": "INFO",
    "log_to_file": True,
    "log_file_path": "logs/app.log",
    "log_max_size": 10,  # MB
    "log_backup_count": 5,
    
    # 系统托盘配置
    "tray_enabled": True,
    "tray_show_notifications": True,
    "tray_notification_duration": 3000,  # 毫秒
    
    # 自动回复配置
    "auto_reply_enabled": True,
    "reply_prefix": "[自动回复] ",
    "reply_suffix": "",
    
    # 状态配置
    "service_status": "stopped",  # stopped, running, error
    "last_activity": None,
    "message_count": 0,
    "reply_count": 0,
}

# 配置文件路径
CONFIG_FILE_NAME = "config.json"
CONFIG_TEMPLATE_NAME = "config-template.json"

# 配置验证规则
CONFIG_VALIDATION = {
    "app_name": {"type": str, "required": True},
    "app_version": {"type": str, "required": True},
    "debug": {"type": bool, "required": False, "default": False},
    "window_width": {"type": int, "required": False, "default": 800, "min": 400, "max": 2000},
    "window_height": {"type": int, "required": False, "default": 600, "min": 300, "max": 1500},
    "mock_enabled": {"type": bool, "required": False, "default": True},
    "mock_reply_text": {"type": str, "required": False, "default": "自动回复"},
    "mock_reply_delay": {"type": int, "required": False, "default": 2, "min": 1, "max": 60},
    "log_level": {"type": str, "required": False, "default": "INFO", 
                  "choices": ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]},
    "auto_reply_enabled": {"type": bool, "required": False, "default": True},
}
