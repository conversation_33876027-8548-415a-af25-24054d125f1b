#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试配置管理器
"""

import sys
import os
sys.path.insert(0, 'minimal_app')

from config.config_manager import Config<PERSON>anager

def test_config_manager():
    """测试配置管理器"""
    try:
        # 创建配置管理器
        cm = ConfigManager()
        
        # 加载配置
        config = cm.load_config()
        
        print("✅ 配置管理器测试成功")
        print(f"应用名称: {config['app_name']}")
        print(f"Mock模式: {config['mock_enabled']}")
        print(f"窗口大小: {config['window_width']}x{config['window_height']}")
        print(f"自动回复文本: {config['mock_reply_text']}")
        
        # 测试设置值
        cm.set_value("mock_reply_text", "测试回复消息")
        new_text = cm.get_value("mock_reply_text")
        print(f"设置后的回复文本: {new_text}")
        
        # 测试保存配置
        if cm.save_config():
            print("✅ 配置保存成功")
        else:
            print("❌ 配置保存失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_config_manager()
