// qstyle.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2023 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file L<PERSON>EN<PERSON> included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStyle : QObject
{
%TypeHeaderCode
#include <qstyle.h>
%End

public:
    QStyle();
    virtual ~QStyle();
    virtual void polish(QWidget *);
    virtual void unpolish(QWidget *);
    virtual void polish(QApplication *);
    virtual void unpolish(QApplication *);
    virtual void polish(QPalette & /In,Out/);
    virtual QRect itemTextRect(const QFontMetrics &fm, const QRect &r, int flags, bool enabled, const QString &text) const;
    virtual QRect itemPixmapRect(const QRect &r, int flags, const QPixmap &pixmap) const;
    virtual void drawItemText(QPainter *painter, const QRect &rectangle, int alignment, const QPalette &palette, bool enabled, const QString &text, QPalette::ColorRole textRole = QPalette::NoRole) const;
    virtual void drawItemPixmap(QPainter *painter, const QRect &rect, int alignment, const QPixmap &pixmap) const;
    virtual QPalette standardPalette() const;

    enum StateFlag
    {
        State_None,
        State_Enabled,
        State_Raised,
        State_Sunken,
        State_Off,
        State_NoChange,
        State_On,
        State_DownArrow,
        State_Horizontal,
        State_HasFocus,
        State_Top,
        State_Bottom,
        State_FocusAtBorder,
        State_AutoRaise,
        State_MouseOver,
        State_UpArrow,
        State_Selected,
        State_Active,
        State_Open,
        State_Children,
        State_Item,
        State_Sibling,
        State_Editing,
        State_KeyboardFocusChange,
        State_ReadOnly,
        State_Window,
        State_Small,
        State_Mini,
    };

    typedef QFlags<QStyle::StateFlag> State;

    enum PrimitiveElement
    {
        PE_Frame,
        PE_FrameDefaultButton,
        PE_FrameDockWidget,
        PE_FrameFocusRect,
        PE_FrameGroupBox,
        PE_FrameLineEdit,
        PE_FrameMenu,
        PE_FrameStatusBar,
        PE_FrameTabWidget,
        PE_FrameWindow,
        PE_FrameButtonBevel,
        PE_FrameButtonTool,
        PE_FrameTabBarBase,
        PE_PanelButtonCommand,
        PE_PanelButtonBevel,
        PE_PanelButtonTool,
        PE_PanelMenuBar,
        PE_PanelToolBar,
        PE_PanelLineEdit,
        PE_IndicatorArrowDown,
        PE_IndicatorArrowLeft,
        PE_IndicatorArrowRight,
        PE_IndicatorArrowUp,
        PE_IndicatorBranch,
        PE_IndicatorButtonDropDown,
        PE_IndicatorViewItemCheck,
        PE_IndicatorCheckBox,
        PE_IndicatorDockWidgetResizeHandle,
        PE_IndicatorHeaderArrow,
        PE_IndicatorMenuCheckMark,
        PE_IndicatorProgressChunk,
        PE_IndicatorRadioButton,
        PE_IndicatorSpinDown,
        PE_IndicatorSpinMinus,
        PE_IndicatorSpinPlus,
        PE_IndicatorSpinUp,
        PE_IndicatorToolBarHandle,
        PE_IndicatorToolBarSeparator,
        PE_PanelTipLabel,
        PE_IndicatorTabTear,
        PE_PanelScrollAreaCorner,
        PE_Widget,
        PE_IndicatorColumnViewArrow,
        PE_FrameStatusBarItem,
        PE_IndicatorItemViewItemCheck,
        PE_IndicatorItemViewItemDrop,
        PE_PanelItemViewItem,
        PE_PanelItemViewRow,
        PE_PanelStatusBar,
        PE_IndicatorTabClose,
        PE_PanelMenu,
%If (Qt_5_7_0 -)
        PE_IndicatorTabTearLeft,
%End
%If (Qt_5_7_0 -)
        PE_IndicatorTabTearRight,
%End
        PE_CustomBase,
    };

    virtual void drawPrimitive(QStyle::PrimitiveElement pe, const QStyleOption *opt, QPainter *p, const QWidget *widget = 0) const = 0;

    enum ControlElement
    {
        CE_PushButton,
        CE_PushButtonBevel,
        CE_PushButtonLabel,
        CE_CheckBox,
        CE_CheckBoxLabel,
        CE_RadioButton,
        CE_RadioButtonLabel,
        CE_TabBarTab,
        CE_TabBarTabShape,
        CE_TabBarTabLabel,
        CE_ProgressBar,
        CE_ProgressBarGroove,
        CE_ProgressBarContents,
        CE_ProgressBarLabel,
        CE_MenuItem,
        CE_MenuScroller,
        CE_MenuVMargin,
        CE_MenuHMargin,
        CE_MenuTearoff,
        CE_MenuEmptyArea,
        CE_MenuBarItem,
        CE_MenuBarEmptyArea,
        CE_ToolButtonLabel,
        CE_Header,
        CE_HeaderSection,
        CE_HeaderLabel,
        CE_ToolBoxTab,
        CE_SizeGrip,
        CE_Splitter,
        CE_RubberBand,
        CE_DockWidgetTitle,
        CE_ScrollBarAddLine,
        CE_ScrollBarSubLine,
        CE_ScrollBarAddPage,
        CE_ScrollBarSubPage,
        CE_ScrollBarSlider,
        CE_ScrollBarFirst,
        CE_ScrollBarLast,
        CE_FocusFrame,
        CE_ComboBoxLabel,
        CE_ToolBar,
        CE_ToolBoxTabShape,
        CE_ToolBoxTabLabel,
        CE_HeaderEmptyArea,
        CE_ColumnViewGrip,
        CE_ItemViewItem,
        CE_ShapedFrame,
        CE_CustomBase,
    };

    virtual void drawControl(QStyle::ControlElement element, const QStyleOption *opt, QPainter *p, const QWidget *widget = 0) const = 0;

    enum SubElement
    {
        SE_PushButtonContents,
        SE_PushButtonFocusRect,
        SE_CheckBoxIndicator,
        SE_CheckBoxContents,
        SE_CheckBoxFocusRect,
        SE_CheckBoxClickRect,
        SE_RadioButtonIndicator,
        SE_RadioButtonContents,
        SE_RadioButtonFocusRect,
        SE_RadioButtonClickRect,
        SE_ComboBoxFocusRect,
        SE_SliderFocusRect,
        SE_ProgressBarGroove,
        SE_ProgressBarContents,
        SE_ProgressBarLabel,
        SE_ToolBoxTabContents,
        SE_HeaderLabel,
        SE_HeaderArrow,
        SE_TabWidgetTabBar,
        SE_TabWidgetTabPane,
        SE_TabWidgetTabContents,
        SE_TabWidgetLeftCorner,
        SE_TabWidgetRightCorner,
        SE_ViewItemCheckIndicator,
        SE_TabBarTearIndicator,
        SE_TreeViewDisclosureItem,
        SE_LineEditContents,
        SE_FrameContents,
        SE_DockWidgetCloseButton,
        SE_DockWidgetFloatButton,
        SE_DockWidgetTitleBarText,
        SE_DockWidgetIcon,
        SE_CheckBoxLayoutItem,
        SE_ComboBoxLayoutItem,
        SE_DateTimeEditLayoutItem,
        SE_DialogButtonBoxLayoutItem,
        SE_LabelLayoutItem,
        SE_ProgressBarLayoutItem,
        SE_PushButtonLayoutItem,
        SE_RadioButtonLayoutItem,
        SE_SliderLayoutItem,
        SE_SpinBoxLayoutItem,
        SE_ToolButtonLayoutItem,
        SE_FrameLayoutItem,
        SE_GroupBoxLayoutItem,
        SE_TabWidgetLayoutItem,
        SE_ItemViewItemCheckIndicator,
        SE_ItemViewItemDecoration,
        SE_ItemViewItemText,
        SE_ItemViewItemFocusRect,
        SE_TabBarTabLeftButton,
        SE_TabBarTabRightButton,
        SE_TabBarTabText,
        SE_ShapedFrameContents,
        SE_ToolBarHandle,
%If (Qt_5_7_0 -)
        SE_TabBarTearIndicatorLeft,
%End
%If (Qt_5_7_0 -)
        SE_TabBarScrollLeftButton,
%End
%If (Qt_5_7_0 -)
        SE_TabBarScrollRightButton,
%End
%If (Qt_5_7_0 -)
        SE_TabBarTearIndicatorRight,
%End
%If (Qt_5_15_0 -)
        SE_PushButtonBevel,
%End
        SE_CustomBase,
    };

    virtual QRect subElementRect(QStyle::SubElement subElement, const QStyleOption *option, const QWidget *widget = 0) const = 0;

    enum ComplexControl
    {
        CC_SpinBox,
        CC_ComboBox,
        CC_ScrollBar,
        CC_Slider,
        CC_ToolButton,
        CC_TitleBar,
        CC_Dial,
        CC_GroupBox,
        CC_MdiControls,
        CC_CustomBase,
    };

    enum SubControl
    {
        SC_None,
        SC_ScrollBarAddLine,
        SC_ScrollBarSubLine,
        SC_ScrollBarAddPage,
        SC_ScrollBarSubPage,
        SC_ScrollBarFirst,
        SC_ScrollBarLast,
        SC_ScrollBarSlider,
        SC_ScrollBarGroove,
        SC_SpinBoxUp,
        SC_SpinBoxDown,
        SC_SpinBoxFrame,
        SC_SpinBoxEditField,
        SC_ComboBoxFrame,
        SC_ComboBoxEditField,
        SC_ComboBoxArrow,
        SC_ComboBoxListBoxPopup,
        SC_SliderGroove,
        SC_SliderHandle,
        SC_SliderTickmarks,
        SC_ToolButton,
        SC_ToolButtonMenu,
        SC_TitleBarSysMenu,
        SC_TitleBarMinButton,
        SC_TitleBarMaxButton,
        SC_TitleBarCloseButton,
        SC_TitleBarNormalButton,
        SC_TitleBarShadeButton,
        SC_TitleBarUnshadeButton,
        SC_TitleBarContextHelpButton,
        SC_TitleBarLabel,
        SC_DialGroove,
        SC_DialHandle,
        SC_DialTickmarks,
        SC_GroupBoxCheckBox,
        SC_GroupBoxLabel,
        SC_GroupBoxContents,
        SC_GroupBoxFrame,
        SC_MdiMinButton,
        SC_MdiNormalButton,
        SC_MdiCloseButton,
        SC_CustomBase,
        SC_All,
    };

    typedef QFlags<QStyle::SubControl> SubControls;
    virtual void drawComplexControl(QStyle::ComplexControl cc, const QStyleOptionComplex *opt, QPainter *p, const QWidget *widget = 0) const = 0;
    virtual QStyle::SubControl hitTestComplexControl(QStyle::ComplexControl cc, const QStyleOptionComplex *opt, const QPoint &pt, const QWidget *widget = 0) const = 0;
    virtual QRect subControlRect(QStyle::ComplexControl cc, const QStyleOptionComplex *opt, QStyle::SubControl sc, const QWidget *widget = 0) const = 0;

    enum PixelMetric
    {
        PM_ButtonMargin,
        PM_ButtonDefaultIndicator,
        PM_MenuButtonIndicator,
        PM_ButtonShiftHorizontal,
        PM_ButtonShiftVertical,
        PM_DefaultFrameWidth,
        PM_SpinBoxFrameWidth,
        PM_ComboBoxFrameWidth,
        PM_MaximumDragDistance,
        PM_ScrollBarExtent,
        PM_ScrollBarSliderMin,
        PM_SliderThickness,
        PM_SliderControlThickness,
        PM_SliderLength,
        PM_SliderTickmarkOffset,
        PM_SliderSpaceAvailable,
        PM_DockWidgetSeparatorExtent,
        PM_DockWidgetHandleExtent,
        PM_DockWidgetFrameWidth,
        PM_TabBarTabOverlap,
        PM_TabBarTabHSpace,
        PM_TabBarTabVSpace,
        PM_TabBarBaseHeight,
        PM_TabBarBaseOverlap,
        PM_ProgressBarChunkWidth,
        PM_SplitterWidth,
        PM_TitleBarHeight,
        PM_MenuScrollerHeight,
        PM_MenuHMargin,
        PM_MenuVMargin,
        PM_MenuPanelWidth,
        PM_MenuTearoffHeight,
        PM_MenuDesktopFrameWidth,
        PM_MenuBarPanelWidth,
        PM_MenuBarItemSpacing,
        PM_MenuBarVMargin,
        PM_MenuBarHMargin,
        PM_IndicatorWidth,
        PM_IndicatorHeight,
        PM_ExclusiveIndicatorWidth,
        PM_ExclusiveIndicatorHeight,
        PM_DialogButtonsSeparator,
        PM_DialogButtonsButtonWidth,
        PM_DialogButtonsButtonHeight,
        PM_MdiSubWindowFrameWidth,
        PM_MDIFrameWidth,
        PM_MdiSubWindowMinimizedWidth,
        PM_MDIMinimizedWidth,
        PM_HeaderMargin,
        PM_HeaderMarkSize,
        PM_HeaderGripMargin,
        PM_TabBarTabShiftHorizontal,
        PM_TabBarTabShiftVertical,
        PM_TabBarScrollButtonWidth,
        PM_ToolBarFrameWidth,
        PM_ToolBarHandleExtent,
        PM_ToolBarItemSpacing,
        PM_ToolBarItemMargin,
        PM_ToolBarSeparatorExtent,
        PM_ToolBarExtensionExtent,
        PM_SpinBoxSliderHeight,
        PM_DefaultTopLevelMargin,
        PM_DefaultChildMargin,
        PM_DefaultLayoutSpacing,
        PM_ToolBarIconSize,
        PM_ListViewIconSize,
        PM_IconViewIconSize,
        PM_SmallIconSize,
        PM_LargeIconSize,
        PM_FocusFrameVMargin,
        PM_FocusFrameHMargin,
        PM_ToolTipLabelFrameWidth,
        PM_CheckBoxLabelSpacing,
        PM_TabBarIconSize,
        PM_SizeGripSize,
        PM_DockWidgetTitleMargin,
        PM_MessageBoxIconSize,
        PM_ButtonIconSize,
        PM_DockWidgetTitleBarButtonMargin,
        PM_RadioButtonLabelSpacing,
        PM_LayoutLeftMargin,
        PM_LayoutTopMargin,
        PM_LayoutRightMargin,
        PM_LayoutBottomMargin,
        PM_LayoutHorizontalSpacing,
        PM_LayoutVerticalSpacing,
        PM_TabBar_ScrollButtonOverlap,
        PM_TextCursorWidth,
        PM_TabCloseIndicatorWidth,
        PM_TabCloseIndicatorHeight,
        PM_ScrollView_ScrollBarSpacing,
        PM_SubMenuOverlap,
        PM_ScrollView_ScrollBarOverlap,
%If (Qt_5_4_0 -)
        PM_TreeViewIndentation,
%End
%If (Qt_5_5_0 -)
        PM_HeaderDefaultSectionSizeHorizontal,
%End
%If (Qt_5_5_0 -)
        PM_HeaderDefaultSectionSizeVertical,
%End
%If (Qt_5_8_0 -)
        PM_TitleBarButtonIconSize,
%End
%If (Qt_5_8_0 -)
        PM_TitleBarButtonSize,
%End
        PM_CustomBase,
    };

    virtual int pixelMetric(QStyle::PixelMetric metric, const QStyleOption *option = 0, const QWidget *widget = 0) const = 0;

    enum ContentsType
    {
        CT_PushButton,
        CT_CheckBox,
        CT_RadioButton,
        CT_ToolButton,
        CT_ComboBox,
        CT_Splitter,
        CT_ProgressBar,
        CT_MenuItem,
        CT_MenuBarItem,
        CT_MenuBar,
        CT_Menu,
        CT_TabBarTab,
        CT_Slider,
        CT_ScrollBar,
        CT_LineEdit,
        CT_SpinBox,
        CT_SizeGrip,
        CT_TabWidget,
        CT_DialogButtons,
        CT_HeaderSection,
        CT_GroupBox,
        CT_MdiControls,
        CT_ItemViewItem,
        CT_CustomBase,
    };

    virtual QSize sizeFromContents(QStyle::ContentsType ct, const QStyleOption *opt, const QSize &contentsSize, const QWidget *widget = 0) const = 0;

    enum StyleHint
    {
        SH_EtchDisabledText,
        SH_DitherDisabledText,
        SH_ScrollBar_MiddleClickAbsolutePosition,
        SH_ScrollBar_ScrollWhenPointerLeavesControl,
        SH_TabBar_SelectMouseType,
        SH_TabBar_Alignment,
        SH_Header_ArrowAlignment,
        SH_Slider_SnapToValue,
        SH_Slider_SloppyKeyEvents,
        SH_ProgressDialog_CenterCancelButton,
        SH_ProgressDialog_TextLabelAlignment,
        SH_PrintDialog_RightAlignButtons,
        SH_MainWindow_SpaceBelowMenuBar,
        SH_FontDialog_SelectAssociatedText,
        SH_Menu_AllowActiveAndDisabled,
        SH_Menu_SpaceActivatesItem,
        SH_Menu_SubMenuPopupDelay,
        SH_ScrollView_FrameOnlyAroundContents,
        SH_MenuBar_AltKeyNavigation,
        SH_ComboBox_ListMouseTracking,
        SH_Menu_MouseTracking,
        SH_MenuBar_MouseTracking,
        SH_ItemView_ChangeHighlightOnFocus,
        SH_Widget_ShareActivation,
        SH_Workspace_FillSpaceOnMaximize,
        SH_ComboBox_Popup,
        SH_TitleBar_NoBorder,
        SH_ScrollBar_StopMouseOverSlider,
        SH_BlinkCursorWhenTextSelected,
        SH_RichText_FullWidthSelection,
        SH_Menu_Scrollable,
        SH_GroupBox_TextLabelVerticalAlignment,
        SH_GroupBox_TextLabelColor,
        SH_Menu_SloppySubMenus,
        SH_Table_GridLineColor,
        SH_LineEdit_PasswordCharacter,
        SH_DialogButtons_DefaultButton,
        SH_ToolBox_SelectedPageTitleBold,
        SH_TabBar_PreferNoArrows,
        SH_ScrollBar_LeftClickAbsolutePosition,
        SH_UnderlineShortcut,
        SH_SpinBox_AnimateButton,
        SH_SpinBox_KeyPressAutoRepeatRate,
        SH_SpinBox_ClickAutoRepeatRate,
        SH_Menu_FillScreenWithScroll,
        SH_ToolTipLabel_Opacity,
        SH_DrawMenuBarSeparator,
        SH_TitleBar_ModifyNotification,
        SH_Button_FocusPolicy,
        SH_MessageBox_UseBorderForButtonSpacing,
        SH_TitleBar_AutoRaise,
        SH_ToolButton_PopupDelay,
        SH_FocusFrame_Mask,
        SH_RubberBand_Mask,
        SH_WindowFrame_Mask,
        SH_SpinControls_DisableOnBounds,
        SH_Dial_BackgroundRole,
        SH_ComboBox_LayoutDirection,
        SH_ItemView_EllipsisLocation,
        SH_ItemView_ShowDecorationSelected,
        SH_ItemView_ActivateItemOnSingleClick,
        SH_ScrollBar_ContextMenu,
        SH_ScrollBar_RollBetweenButtons,
        SH_Slider_StopMouseOverSlider,
        SH_Slider_AbsoluteSetButtons,
        SH_Slider_PageSetButtons,
        SH_Menu_KeyboardSearch,
        SH_TabBar_ElideMode,
        SH_DialogButtonLayout,
        SH_ComboBox_PopupFrameStyle,
        SH_MessageBox_TextInteractionFlags,
        SH_DialogButtonBox_ButtonsHaveIcons,
        SH_SpellCheckUnderlineStyle,
        SH_MessageBox_CenterButtons,
        SH_Menu_SelectionWrap,
        SH_ItemView_MovementWithoutUpdatingSelection,
        SH_ToolTip_Mask,
        SH_FocusFrame_AboveWidget,
        SH_TextControl_FocusIndicatorTextCharFormat,
        SH_WizardStyle,
        SH_ItemView_ArrowKeysNavigateIntoChildren,
        SH_Menu_Mask,
        SH_Menu_FlashTriggeredItem,
        SH_Menu_FadeOutOnHide,
        SH_SpinBox_ClickAutoRepeatThreshold,
        SH_ItemView_PaintAlternatingRowColorsForEmptyArea,
        SH_FormLayoutWrapPolicy,
        SH_TabWidget_DefaultTabPosition,
        SH_ToolBar_Movable,
        SH_FormLayoutFieldGrowthPolicy,
        SH_FormLayoutFormAlignment,
        SH_FormLayoutLabelAlignment,
        SH_ItemView_DrawDelegateFrame,
        SH_TabBar_CloseButtonPosition,
        SH_DockWidget_ButtonsHaveFrame,
        SH_ToolButtonStyle,
        SH_RequestSoftwareInputPanel,
        SH_ListViewExpand_SelectMouseType,
        SH_ScrollBar_Transient,
%If (Qt_5_1_0 -)
        SH_Menu_SupportsSections,
%End
%If (Qt_5_2_0 -)
        SH_ToolTip_WakeUpDelay,
%End
%If (Qt_5_2_0 -)
        SH_ToolTip_FallAsleepDelay,
%End
%If (Qt_5_2_0 -)
        SH_Widget_Animate,
%End
%If (Qt_5_2_0 -)
        SH_Splitter_OpaqueResize,
%End
%If (Qt_5_4_0 -)
        SH_LineEdit_PasswordMaskDelay,
%End
%If (Qt_5_4_0 -)
        SH_TabBar_ChangeCurrentDelay,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuUniDirection,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuUniDirectionFailCount,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuSloppySelectOtherActions,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuSloppyCloseTimeout,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuResetWhenReenteringParent,
%End
%If (Qt_5_5_0 -)
        SH_Menu_SubMenuDontStartSloppyOnLeave,
%End
%If (Qt_5_7_0 -)
        SH_ItemView_ScrollMode,
%End
%If (Qt_5_10_0 -)
        SH_TitleBar_ShowToolTipsOnButtons,
%End
%If (Qt_5_10_0 -)
        SH_Widget_Animation_Duration,
%End
%If (Qt_5_11_0 -)
        SH_ComboBox_AllowWheelScrolling,
%End
%If (Qt_5_11_0 -)
        SH_SpinBox_ButtonsInsideFrame,
%End
%If (Qt_5_12_0 -)
        SH_SpinBox_StepModifier,
%End
        SH_CustomBase,
    };

    virtual int styleHint(QStyle::StyleHint stylehint, const QStyleOption *option = 0, const QWidget *widget = 0, QStyleHintReturn *returnData = 0) const = 0;

    enum StandardPixmap
    {
        SP_TitleBarMenuButton,
        SP_TitleBarMinButton,
        SP_TitleBarMaxButton,
        SP_TitleBarCloseButton,
        SP_TitleBarNormalButton,
        SP_TitleBarShadeButton,
        SP_TitleBarUnshadeButton,
        SP_TitleBarContextHelpButton,
        SP_DockWidgetCloseButton,
        SP_MessageBoxInformation,
        SP_MessageBoxWarning,
        SP_MessageBoxCritical,
        SP_MessageBoxQuestion,
        SP_DesktopIcon,
        SP_TrashIcon,
        SP_ComputerIcon,
        SP_DriveFDIcon,
        SP_DriveHDIcon,
        SP_DriveCDIcon,
        SP_DriveDVDIcon,
        SP_DriveNetIcon,
        SP_DirOpenIcon,
        SP_DirClosedIcon,
        SP_DirLinkIcon,
        SP_FileIcon,
        SP_FileLinkIcon,
        SP_ToolBarHorizontalExtensionButton,
        SP_ToolBarVerticalExtensionButton,
        SP_FileDialogStart,
        SP_FileDialogEnd,
        SP_FileDialogToParent,
        SP_FileDialogNewFolder,
        SP_FileDialogDetailedView,
        SP_FileDialogInfoView,
        SP_FileDialogContentsView,
        SP_FileDialogListView,
        SP_FileDialogBack,
        SP_DirIcon,
        SP_DialogOkButton,
        SP_DialogCancelButton,
        SP_DialogHelpButton,
        SP_DialogOpenButton,
        SP_DialogSaveButton,
        SP_DialogCloseButton,
        SP_DialogApplyButton,
        SP_DialogResetButton,
        SP_DialogDiscardButton,
        SP_DialogYesButton,
        SP_DialogNoButton,
        SP_ArrowUp,
        SP_ArrowDown,
        SP_ArrowLeft,
        SP_ArrowRight,
        SP_ArrowBack,
        SP_ArrowForward,
        SP_DirHomeIcon,
        SP_CommandLink,
        SP_VistaShield,
        SP_BrowserReload,
        SP_BrowserStop,
        SP_MediaPlay,
        SP_MediaStop,
        SP_MediaPause,
        SP_MediaSkipForward,
        SP_MediaSkipBackward,
        SP_MediaSeekForward,
        SP_MediaSeekBackward,
        SP_MediaVolume,
        SP_MediaVolumeMuted,
        SP_DirLinkOpenIcon,
%If (Qt_5_2_0 -)
        SP_LineEditClearButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogYesToAllButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogNoToAllButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogSaveAllButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogAbortButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogRetryButton,
%End
%If (Qt_5_14_0 -)
        SP_DialogIgnoreButton,
%End
%If (Qt_5_14_0 -)
        SP_RestoreDefaultsButton,
%End
        SP_CustomBase,
    };

    virtual QPixmap standardPixmap(QStyle::StandardPixmap standardPixmap, const QStyleOption *option = 0, const QWidget *widget = 0) const = 0;
    virtual QIcon standardIcon(QStyle::StandardPixmap standardIcon, const QStyleOption *option = 0, const QWidget *widget = 0) const = 0;
    virtual QPixmap generatedIconPixmap(QIcon::Mode iconMode, const QPixmap &pixmap, const QStyleOption *opt) const = 0;
    static QRect visualRect(Qt::LayoutDirection direction, const QRect &boundingRect, const QRect &logicalRect);
    static QPoint visualPos(Qt::LayoutDirection direction, const QRect &boundingRect, const QPoint &logicalPos);
    static int sliderPositionFromValue(int min, int max, int logicalValue, int span, bool upsideDown = false);
    static int sliderValueFromPosition(int min, int max, int position, int span, bool upsideDown = false);
    static Qt::Alignment visualAlignment(Qt::LayoutDirection direction, Qt::Alignment alignment);
    static QRect alignedRect(Qt::LayoutDirection direction, Qt::Alignment alignment, const QSize &size, const QRect &rectangle);
    virtual int layoutSpacing(QSizePolicy::ControlType control1, QSizePolicy::ControlType control2, Qt::Orientation orientation, const QStyleOption *option = 0, const QWidget *widget = 0) const = 0;
    int combinedLayoutSpacing(QSizePolicy::ControlTypes controls1, QSizePolicy::ControlTypes controls2, Qt::Orientation orientation, QStyleOption *option = 0, QWidget *widget = 0) const;

    enum RequestSoftwareInputPanel
    {
        RSIP_OnMouseClickAndAlreadyFocused,
        RSIP_OnMouseClick,
    };

    const QStyle *proxy() const;
};

QFlags<QStyle::StateFlag> operator|(QStyle::StateFlag f1, QFlags<QStyle::StateFlag> f2);
QFlags<QStyle::SubControl> operator|(QStyle::SubControl f1, QFlags<QStyle::SubControl> f2);
