# -*- coding: utf-8 -*-
"""
消息处理器模块
"""

import time
import threading
from typing import Dict, List, Callable, Optional
from datetime import datetime
from .mock_engine import MockEngine


class MessageHandler:
    """消息处理器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager
        self.mock_engine = MockEngine()
        self.is_running = False
        self.auto_reply_enabled = True
        self.message_callbacks = []
        self.reply_callbacks = []
        self._worker_thread = None
        self._stop_event = threading.Event()
    
    def start_service(self) -> bool:
        """
        启动消息服务
        
        Returns:
            是否启动成功
        """
        try:
            if self.is_running:
                print("⚠️ 消息服务已在运行")
                return True
            
            # 启动Mock引擎
            if not self.mock_engine.start():
                return False
            
            # 启动工作线程
            self._stop_event.clear()
            self._worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self._worker_thread.start()
            
            self.is_running = True
            print("✅ 消息服务启动成功")
            return True
            
        except Exception as e:
            print(f"❌ 消息服务启动失败: {e}")
            return False
    
    def stop_service(self) -> bool:
        """
        停止消息服务
        
        Returns:
            是否停止成功
        """
        try:
            if not self.is_running:
                print("⚠️ 消息服务未运行")
                return True
            
            # 停止工作线程
            self._stop_event.set()
            if self._worker_thread and self._worker_thread.is_alive():
                self._worker_thread.join(timeout=5)
            
            # 停止Mock引擎
            self.mock_engine.stop()
            
            self.is_running = False
            print("✅ 消息服务已停止")
            return True
            
        except Exception as e:
            print(f"❌ 消息服务停止失败: {e}")
            return False
    
    def process_message(self, message: str) -> str:
        """
        处理消息并返回回复
        
        Args:
            message: 输入消息
            
        Returns:
            回复消息
        """
        try:
            if not self.is_running:
                return "服务未启动"
            
            # 获取配置
            reply_text = "这是一个自动回复消息"
            if self.config_manager:
                reply_text = self.config_manager.get_value("mock_reply_text", reply_text)
            
            # 生成回复
            mock_msg = {"content": message, "user": "用户"}
            reply = self.mock_engine.generate_auto_reply(mock_msg, reply_text)
            
            return reply["content"] if reply else "回复生成失败"
            
        except Exception as e:
            print(f"❌ 消息处理失败: {e}")
            return "消息处理出错"
    
    def get_status(self) -> Dict:
        """
        获取服务状态
        
        Returns:
            状态字典
        """
        mock_status = self.mock_engine.get_status()
        return {
            "service_running": self.is_running,
            "auto_reply_enabled": self.auto_reply_enabled,
            "mock_engine_status": mock_status,
            "worker_thread_alive": self._worker_thread.is_alive() if self._worker_thread else False
        }
    
    def add_message_callback(self, callback: Callable):
        """
        添加消息回调函数
        
        Args:
            callback: 回调函数
        """
        if callback not in self.message_callbacks:
            self.message_callbacks.append(callback)
    
    def add_reply_callback(self, callback: Callable):
        """
        添加回复回调函数
        
        Args:
            callback: 回调函数
        """
        if callback not in self.reply_callbacks:
            self.reply_callbacks.append(callback)
    
    def remove_message_callback(self, callback: Callable):
        """移除消息回调函数"""
        if callback in self.message_callbacks:
            self.message_callbacks.remove(callback)
    
    def remove_reply_callback(self, callback: Callable):
        """移除回复回调函数"""
        if callback in self.reply_callbacks:
            self.reply_callbacks.remove(callback)
    
    def _worker_loop(self):
        """工作线程循环"""
        try:
            while not self._stop_event.is_set():
                if self.is_running and self.auto_reply_enabled:
                    # 生成Mock消息
                    message = self.mock_engine.generate_mock_message()
                    if message:
                        # 通知消息回调
                        for callback in self.message_callbacks:
                            try:
                                callback(message)
                            except Exception as e:
                                print(f"❌ 消息回调执行失败: {e}")
                        
                        # 生成自动回复
                        reply_text = "这是一个自动回复消息"
                        if self.config_manager:
                            reply_text = self.config_manager.get_value("mock_reply_text", reply_text)
                            reply_delay = self.config_manager.get_value("mock_reply_delay", 2)
                        else:
                            reply_delay = 2
                        
                        # 延迟回复
                        time.sleep(reply_delay)
                        
                        reply = self.mock_engine.generate_auto_reply(message, reply_text)
                        if reply:
                            # 通知回复回调
                            for callback in self.reply_callbacks:
                                try:
                                    callback(reply)
                                except Exception as e:
                                    print(f"❌ 回复回调执行失败: {e}")
                
                # 等待下一次循环
                interval = 10  # 默认10秒
                if self.config_manager:
                    interval = self.config_manager.get_value("mock_message_interval", 10)
                
                self._stop_event.wait(interval)
                
        except Exception as e:
            print(f"❌ 工作线程异常: {e}")
        finally:
            print("🔄 工作线程已退出")
