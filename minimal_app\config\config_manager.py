# -*- coding: utf-8 -*-
"""
配置管理器
"""

import json
import os
import copy
from typing import Any, Dict, Optional
from .default_config import DEFAULT_CONFIG, CONFIG_FILE_NAME, CONFIG_TEMPLATE_NAME, CONFIG_VALIDATION


class ConfigManager:
    """配置管理器类"""
    
    def __init__(self, config_dir: str = "."):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = config_dir
        self.config_file = os.path.join(config_dir, CONFIG_FILE_NAME)
        self.template_file = os.path.join(config_dir, "resources", "config", CONFIG_TEMPLATE_NAME)
        self._config = copy.deepcopy(DEFAULT_CONFIG)
        self._loaded = False
    
    def load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            # 首先尝试加载用户配置文件
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    self._merge_config(user_config)
                    print(f"✅ 配置文件加载成功: {self.config_file}")
            
            # 如果用户配置不存在，尝试从模板创建
            elif os.path.exists(self.template_file):
                with open(self.template_file, 'r', encoding='utf-8') as f:
                    template_config = json.load(f)
                    self._merge_config(template_config)
                    print(f"✅ 从模板加载配置: {self.template_file}")
                    # 保存为用户配置
                    self.save_config()
            
            else:
                print("⚠️ 配置文件不存在，使用默认配置")
                # 创建默认配置文件
                self.save_config()
            
            self._loaded = True
            self._validate_config()
            return self._config
            
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            print("使用默认配置")
            self._config = copy.deepcopy(DEFAULT_CONFIG)
            return self._config
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            是否保存成功
        """
        try:
            # 确保配置目录存在
            os.makedirs(self.config_dir, exist_ok=True)
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            
            print(f"✅ 配置保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 配置保存失败: {e}")
            return False
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        if not self._loaded:
            self.load_config()
        
        return self._config.get(key, default)
    
    def set_value(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            # 验证配置值
            if not self._validate_value(key, value):
                return False
            
            self._config[key] = value
            return True
            
        except Exception as e:
            print(f"❌ 设置配置失败 {key}={value}: {e}")
            return False
    
    def get_all_config(self) -> Dict[str, Any]:
        """
        获取所有配置
        
        Returns:
            配置字典副本
        """
        if not self._loaded:
            self.load_config()
        
        return copy.deepcopy(self._config)
    
    def update_config(self, config_dict: Dict[str, Any]) -> bool:
        """
        批量更新配置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            是否更新成功
        """
        try:
            for key, value in config_dict.items():
                if not self.set_value(key, value):
                    return False
            return True
            
        except Exception as e:
            print(f"❌ 批量更新配置失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """
        重置为默认配置
        
        Returns:
            是否重置成功
        """
        try:
            self._config = copy.deepcopy(DEFAULT_CONFIG)
            return self.save_config()
            
        except Exception as e:
            print(f"❌ 重置配置失败: {e}")
            return False
    
    def _merge_config(self, user_config: Dict[str, Any]):
        """
        合并用户配置到默认配置
        
        Args:
            user_config: 用户配置
        """
        for key, value in user_config.items():
            if key in DEFAULT_CONFIG:
                self._config[key] = value
            else:
                print(f"⚠️ 忽略未知配置项: {key}")
    
    def _validate_config(self):
        """验证配置完整性"""
        for key, rule in CONFIG_VALIDATION.items():
            if rule.get("required", False) and key not in self._config:
                print(f"⚠️ 缺少必需配置项: {key}")
                if "default" in rule:
                    self._config[key] = rule["default"]
    
    def _validate_value(self, key: str, value: Any) -> bool:
        """
        验证配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            是否有效
        """
        if key not in CONFIG_VALIDATION:
            return True  # 未定义验证规则的键允许任意值
        
        rule = CONFIG_VALIDATION[key]
        
        # 类型检查
        if "type" in rule and not isinstance(value, rule["type"]):
            print(f"❌ 配置值类型错误 {key}: 期望 {rule['type'].__name__}, 实际 {type(value).__name__}")
            return False
        
        # 数值范围检查
        if isinstance(value, (int, float)):
            if "min" in rule and value < rule["min"]:
                print(f"❌ 配置值过小 {key}: {value} < {rule['min']}")
                return False
            if "max" in rule and value > rule["max"]:
                print(f"❌ 配置值过大 {key}: {value} > {rule['max']}")
                return False
        
        # 选择项检查
        if "choices" in rule and value not in rule["choices"]:
            print(f"❌ 配置值不在允许范围 {key}: {value} not in {rule['choices']}")
            return False
        
        return True
