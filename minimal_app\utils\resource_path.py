# -*- coding: utf-8 -*-
"""
资源路径处理模块
"""

import os
import sys


def resource_path(relative_path: str) -> str:
    """
    获取资源文件的绝对路径，兼容打包后的环境
    
    Args:
        relative_path: 相对路径
        
    Returns:
        绝对路径
    """
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except AttributeError:
        # 开发环境中使用当前文件的目录
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    return os.path.join(base_path, relative_path)


def get_resource_path(resource_type: str, filename: str) -> str:
    """
    获取特定类型资源的路径
    
    Args:
        resource_type: 资源类型 (icons, config, etc.)
        filename: 文件名
        
    Returns:
        资源文件路径
    """
    return resource_path(os.path.join("resources", resource_type, filename))


def get_app_dir() -> str:
    """
    获取应用程序目录
    
    Returns:
        应用程序目录路径
    """
    if hasattr(sys, '_MEIPASS'):
        # 打包后的环境
        return os.path.dirname(sys.executable)
    else:
        # 开发环境
        return os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
