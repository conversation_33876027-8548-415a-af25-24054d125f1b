# -*- coding: utf-8 -*-
"""
Mock引擎模块
"""

import time
import random
from typing import Dict, List, Optional
from datetime import datetime


class MockEngine:
    """Mock消息引擎"""
    
    def __init__(self):
        self.is_running = False
        self.message_count = 0
        self.reply_count = 0
        self.last_activity = None
        
        # Mock消息模板
        self.mock_messages = [
            "你好，请问有什么可以帮助您的吗？",
            "今天天气怎么样？",
            "请帮我查询一下订单状态",
            "什么时候可以发货？",
            "产品价格是多少？",
            "有没有优惠活动？",
            "客服在线吗？",
            "谢谢您的帮助！",
            "这个问题解决了吗？",
            "我需要技术支持"
        ]
        
        # Mock用户名
        self.mock_users = [
            "张三", "李四", "王五", "赵六", "钱七",
            "孙八", "周九", "吴十", "郑十一", "王十二"
        ]
    
    def start(self) -> bool:
        """
        启动Mock引擎
        
        Returns:
            是否启动成功
        """
        try:
            self.is_running = True
            self.last_activity = datetime.now()
            print("✅ Mock引擎启动成功")
            return True
        except Exception as e:
            print(f"❌ Mock引擎启动失败: {e}")
            return False
    
    def stop(self) -> bool:
        """
        停止Mock引擎
        
        Returns:
            是否停止成功
        """
        try:
            self.is_running = False
            print("✅ Mock引擎已停止")
            return True
        except Exception as e:
            print(f"❌ Mock引擎停止失败: {e}")
            return False
    
    def generate_mock_message(self) -> Dict:
        """
        生成Mock消息
        
        Returns:
            消息字典
        """
        if not self.is_running:
            return None
        
        try:
            message = {
                "id": f"msg_{self.message_count + 1}",
                "user": random.choice(self.mock_users),
                "content": random.choice(self.mock_messages),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "type": "text"
            }
            
            self.message_count += 1
            self.last_activity = datetime.now()
            
            return message
            
        except Exception as e:
            print(f"❌ 生成Mock消息失败: {e}")
            return None
    
    def generate_auto_reply(self, message: Dict, reply_text: str = None) -> Dict:
        """
        生成自动回复
        
        Args:
            message: 原始消息
            reply_text: 回复文本
            
        Returns:
            回复消息字典
        """
        if not self.is_running:
            return None
        
        try:
            if not reply_text:
                reply_text = "感谢您的消息，我们已收到并会尽快回复。"
            
            reply = {
                "id": f"reply_{self.reply_count + 1}",
                "user": "系统",
                "content": f"[自动回复] {reply_text}",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "type": "reply",
                "original_message_id": message.get("id") if message else None
            }
            
            self.reply_count += 1
            self.last_activity = datetime.now()
            
            return reply
            
        except Exception as e:
            print(f"❌ 生成自动回复失败: {e}")
            return None
    
    def get_status(self) -> Dict:
        """
        获取引擎状态
        
        Returns:
            状态字典
        """
        return {
            "is_running": self.is_running,
            "message_count": self.message_count,
            "reply_count": self.reply_count,
            "last_activity": self.last_activity.strftime("%Y-%m-%d %H:%M:%S") if self.last_activity else None,
            "uptime": str(datetime.now() - self.last_activity) if self.last_activity else "0:00:00"
        }
    
    def reset_counters(self):
        """重置计数器"""
        self.message_count = 0
        self.reply_count = 0
        self.last_activity = None
        print("✅ Mock引擎计数器已重置")
